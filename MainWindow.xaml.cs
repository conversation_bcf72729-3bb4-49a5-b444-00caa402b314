using System.Windows;
using ApartmanYonetimSistemi.Views;

namespace ApartmanYonetimSistemi;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        PasswordBox.Password = "123456"; // Test için

        // Enter tuşu desteği
        EmailTextBox.KeyDown += (s, e) => { if (e.Key == System.Windows.Input.Key.Enter) LoginButton_Click(s, e); };
        PasswordBox.KeyDown += (s, e) => { if (e.Key == System.Windows.Input.Key.Enter) LoginButton_Click(s, e); };
    }

    private void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            string email = EmailTextBox.Text;
            string password = PasswordBox.Password;

            // Log dosyasına giriş denemesi yaz
            System.IO.File.AppendAllText("login_attempts.log",
                $"Giriş denemesi: {email} - {DateTime.Now}\n");

            if (email == "<EMAIL>" && password == "123456")
            {
                // Log dosyasına başarılı giriş yaz
                System.IO.File.AppendAllText("login_attempts.log",
                    $"Başarılı giriş: {email} - {DateTime.Now}\n");

                // Ana pencereyi oluştur ve göster
                var mainWindow = new Window
                {
                    Title = "Apartman Yönetim Sistemi - Ana Panel",
                    Content = new MainView(),
                    WindowState = WindowState.Maximized,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                mainWindow.Show();
                this.Close(); // Login penceresini kapat
            }
            else
            {
                // Log dosyasına başarısız giriş yaz
                System.IO.File.AppendAllText("login_attempts.log",
                    $"Başarısız giriş: {email} - {DateTime.Now}\n");

                MessageBox.Show("Hatalı e-posta veya şifre!\nDoğru bilgiler:\nE-posta: <EMAIL>\nŞifre: 123456",
                    "Hata", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            // Hata durumunda log dosyasına yaz
            System.IO.File.AppendAllText("error.log",
                $"Login hatası: {ex.Message} - {DateTime.Now}\n");

            MessageBox.Show($"Bir hata oluştu: {ex.Message}", "Hata",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
