using ApartmanYonetimSistemi.Models;
using System.Threading.Tasks;
using AppUser = ApartmanYonetimSistemi.Models.User;

namespace ApartmanYonetimSistemi.Services
{
    public class AuthService
    {
        private readonly FirebaseService _firebaseService;
        private AppUser? _currentUser;

        public AuthService()
        {
            _firebaseService = new FirebaseService();
        }

        public AppUser? CurrentUser => _currentUser;
        public bool IsAuthenticated => _currentUser != null;

        public async Task<(bool Success, string Message, AppUser? User)> LoginAsync(string email, string password)
        {
            try
            {
                // Basit mock authentication - gerçek projede Firebase Auth kullanılacak
                if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
                {
                    return (false, "E-posta ve şifre gereklidir", null);
                }

                // Test kullanıcısı kontrolü
                if (email == "<EMAIL>" && password == "123456")
                {
                    _currentUser = new AppUser
                    {
                        Id = "admin-test-id",
                        Email = email,
                        Name = "Test Admin",
                        Role = UserRoles.Admin,
                        IsActive = true,
                        LastLoginDate = DateTime.Now
                    };
                    return (true, "Giriş başarılı!", _currentUser);
                }

                // Firestore'dan kullanıcı ara
                _currentUser = await _firebaseService.GetUserByEmailAsync(email);

                if (_currentUser != null && _currentUser.IsActive)
                {
                    // Gerçek projede şifre kontrolü yapılacak
                    await _firebaseService.UpdateUserLastLoginAsync(_currentUser.Id);
                    return (true, "Giriş başarılı!", _currentUser);
                }

                return (false, "Geçersiz e-posta veya şifre", null);
            }
            catch (Exception ex)
            {
                return (false, $"Giriş sırasında hata oluştu: {ex.Message}", null);
            }
        }

        public async Task<(bool Success, string Message, AppUser? User)> RegisterAsync(string email, string password, string name, string company = "")
        {
            try
            {
                // E-posta kontrolü
                var existingUser = await _firebaseService.GetUserByEmailAsync(email);
                if (existingUser != null)
                {
                    return (false, "Bu e-posta adresi zaten kullanımda", null);
                }

                // Yeni kullanıcı oluştur
                var newUser = new AppUser
                {
                    Email = email,
                    Name = name,
                    Company = company,
                    Role = UserRoles.Manager,
                    IsActive = true
                };

                string userId = await _firebaseService.AddUserAsync(newUser);
                newUser.Id = userId;

                return (true, "Kayıt başarılı! Giriş yapabilirsiniz.", newUser);
            }
            catch (Exception ex)
            {
                return (false, $"Kayıt sırasında hata oluştu: {ex.Message}", null);
            }
        }

        public async Task<(bool Success, string Message)> ResetPasswordAsync(string email)
        {
            try
            {
                var user = await _firebaseService.GetUserByEmailAsync(email);
                if (user == null)
                {
                    return (false, "Bu e-posta adresi ile kayıtlı kullanıcı bulunamadı");
                }

                // Gerçek projede e-posta gönderilecek
                return (true, "Şifre sıfırlama e-postası gönderildi (simülasyon)");
            }
            catch (Exception ex)
            {
                return (false, $"Şifre sıfırlama sırasında hata oluştu: {ex.Message}");
            }
        }

        public async Task<(bool Success, string Message)> ChangePasswordAsync(string currentPassword, string newPassword)
        {
            try
            {
                if (_currentUser == null)
                {
                    return (false, "Kullanıcı oturumu bulunamadı");
                }

                if (string.IsNullOrEmpty(newPassword) || newPassword.Length < 6)
                {
                    return (false, "Yeni şifre en az 6 karakter olmalıdır");
                }

                // Gerçek projede şifre değiştirme işlemi yapılacak
                await Task.Delay(100); // Simülasyon için kısa bekleme
                return (true, "Şifre başarıyla değiştirildi (simülasyon)");
            }
            catch (Exception ex)
            {
                return (false, $"Şifre değiştirme sırasında hata oluştu: {ex.Message}");
            }
        }

        public void Logout()
        {
            _currentUser = null;
        }

        public async Task<bool> RefreshTokenAsync()
        {
            // Gerçek projede token yenileme işlemi yapılacak
            await Task.Delay(100);
            return _currentUser != null;
        }

        public bool HasPermission(string requiredRole)
        {
            if (_currentUser == null) return false;

            return _currentUser.Role switch
            {
                UserRoles.Admin => true,
                UserRoles.Manager => requiredRole != UserRoles.Admin,
                UserRoles.Viewer => requiredRole == UserRoles.Viewer,
                _ => false
            };
        }

        public bool CanAccessSite(string siteId)
        {
            if (_currentUser == null) return false;

            // Admin tüm sitelere erişebilir
            if (_currentUser.Role == UserRoles.Admin) return true;

            // Diğer kullanıcılar sadece yetkili oldukları sitelere erişebilir
            return _currentUser.SiteIds.Contains(siteId);
        }
    }
}