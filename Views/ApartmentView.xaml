<UserControl x:Class="ApartmanYonetimSistemi.Views.ApartmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources">
    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:ApartmentViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Apartman Listesi -->
        <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Apartmanlar"
                         Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Apartman Listesi -->
                <DataGrid Grid.Row="2"
                        ItemsSource="{Binding Apartments}"
                        SelectedItem="{Binding SelectedApartment, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Apartman Adı" Binding="{Binding ApartmentName}" Width="*"/>
                        <DataGridTextColumn Header="Adres" Binding="{Binding Address}" Width="2*"/>
                        <DataGridTextColumn Header="Toplam Daire" Binding="{Binding TotalFlats}" Width="Auto"/>
                        <DataGridTextColumn Header="Oluşturma Tarihi" Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" Width="Auto"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="YENİ APARTMAN"
                          Command="{Binding NewApartmentCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="DÜZENLE"
                          Command="{Binding EditApartmentCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="SİL"
                          Command="{Binding DeleteApartmentCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Apartman Detay/Düzenleme Formu -->
        <materialDesign:Card Grid.Column="1">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0"
                         Text="{Binding IsEditing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Apartman Düzenle|Yeni Apartman'}"
                         Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBox materialDesign:HintAssist.Hint="Apartman Adı"
                               Text="{Binding CurrentApartment.ApartmentName, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <TextBox materialDesign:HintAssist.Hint="Adres"
                               Text="{Binding CurrentApartment.Address, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <TextBox materialDesign:HintAssist.Hint="Toplam Daire Sayısı"
                               Text="{Binding CurrentApartment.TotalFlats, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <CheckBox Content="Aktif"
                                IsChecked="{Binding CurrentApartment.IsActive}"
                                Style="{DynamicResource MaterialDesignCheckBox}"
                                Margin="0,8,0,0"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Margin="0,16,0,0">
                    <Button Content="KAYDET"
                          Command="{Binding SaveApartmentCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,0,8"/>
                    <Button Content="İPTAL"
                          Command="{Binding CancelEditCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.ColumnSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>