<UserControl x:Class="ApartmanYonetimSistemi.Views.FlatView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources">
    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:FlatViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Daire Listesi -->
        <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Daireler"
                         Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Daire Listesi -->
                <DataGrid Grid.Row="2"
                        ItemsSource="{Binding Flats}"
                        SelectedItem="{Binding SelectedFlat, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Daire No" Binding="{Binding FlatNo}" Width="Auto"/>
                        <DataGridTextColumn Header="Kira" Binding="{Binding RentAmount, StringFormat=C}" Width="Auto"/>
                        <DataGridTextColumn Header="Aidat" Binding="{Binding Dues, StringFormat=C}" Width="Auto"/>
                        <DataGridTextColumn Header="Kira Durumu" Binding="{Binding RentStatus}" Width="Auto"/>
                        <DataGridTextColumn Header="Aidat Durumu" Binding="{Binding DuesStatus}" Width="Auto"/>
                        <DataGridCheckBoxColumn Header="Dolu" Binding="{Binding IsOccupied}" Width="Auto"/>
                        <DataGridTextColumn Header="Oluşturma Tarihi" Binding="{Binding CreatedDate, StringFormat=dd.MM.yyyy}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="YENİ DAİRE"
                          Command="{Binding NewFlatCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="DÜZENLE"
                          Command="{Binding EditFlatCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="SİL"
                          Command="{Binding DeleteFlatCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Daire Detay/Düzenleme Formu -->
        <materialDesign:Card Grid.Column="1">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0"
                         Text="{Binding IsEditing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Daire Düzenle|Yeni Daire'}"
                         Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Daire Numarası -->
                        <TextBox materialDesign:HintAssist.Hint="Daire Numarası"
                               Text="{Binding CurrentFlat.FlatNo, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Kira Bedeli -->
                        <TextBox materialDesign:HintAssist.Hint="Kira Bedeli (₺)"
                               Text="{Binding CurrentFlat.RentAmount, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Aidat -->
                        <TextBox materialDesign:HintAssist.Hint="Aidat (₺)"
                               Text="{Binding CurrentFlat.Dues, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Kira Durumu -->
                        <ComboBox materialDesign:HintAssist.Hint="Kira Durumu"
                                SelectedValue="{Binding CurrentFlat.RentStatus}"
                                SelectedValuePath="Tag"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16">
                            <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                            <ComboBoxItem Content="Ödenmedi" Tag="Due"/>
                        </ComboBox>

                        <!-- Aidat Durumu -->
                        <ComboBox materialDesign:HintAssist.Hint="Aidat Durumu"
                                SelectedValue="{Binding CurrentFlat.DuesStatus}"
                                SelectedValuePath="Tag"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16">
                            <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                            <ComboBoxItem Content="Ödenmedi" Tag="Due"/>
                        </ComboBox>

                        <!-- Kira Son Ödeme Tarihi -->
                        <DatePicker materialDesign:HintAssist.Hint="Kira Son Ödeme Tarihi"
                                  SelectedDate="{Binding CurrentFlat.RentDueDate}"
                                  Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="0,0,0,16"/>

                        <!-- Aidat Son Ödeme Tarihi -->
                        <DatePicker materialDesign:HintAssist.Hint="Aidat Son Ödeme Tarihi"
                                  SelectedDate="{Binding CurrentFlat.DuesDueDate}"
                                  Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="0,0,0,16"/>

                        <!-- Dolu/Boş Durumu -->
                        <CheckBox Content="Daire Dolu"
                                IsChecked="{Binding CurrentFlat.IsOccupied}"
                                Style="{DynamicResource MaterialDesignCheckBox}"
                                Margin="0,8,0,16"/>

                        <!-- Aktif Durumu -->
                        <CheckBox Content="Aktif"
                                IsChecked="{Binding CurrentFlat.IsActive}"
                                Style="{DynamicResource MaterialDesignCheckBox}"
                                Margin="0,8,0,0"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Margin="0,16,0,0">
                    <Button Content="KAYDET"
                          Command="{Binding SaveFlatCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,0,8"/>
                    <Button Content="İPTAL"
                          Command="{Binding CancelEditCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.ColumnSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>