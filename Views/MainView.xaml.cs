using System.Windows.Controls;
using ApartmanYonetimSistemi.ViewModels;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Views
{
    /// <summary>
    /// Interaction logic for MainView.xaml
    /// </summary>
    public partial class MainView : UserControl
    {
        private MainViewModel? _mainViewModel;

        public MainView()
        {
            InitializeComponent();
            DataContextChanged += OnDataContextChanged;
            Loaded += OnLoaded;
        }

        private async void OnLoaded(object sender, System.Windows.RoutedEventArgs e)
        {
            _mainViewModel = DataContext as MainViewModel;
            if (_mainViewModel != null)
            {
                // Test kullanıcısı oluştur
                var testUser = new User
                {
                    Id = "test-user-1",
                    Name = "Admin Kullanıcı",
                    Email = "<EMAIL>",
                    Role = UserRoles.Admin
                };

                await _mainViewModel.InitializeAsync(testUser);
                _mainViewModel.PropertyChanged += OnMainViewModelPropertyChanged;
            }
        }

        private void OnDataContextChanged(object sender, System.Windows.DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue is MainViewModel mainViewModel)
            {
                _mainViewModel = mainViewModel;
                _mainViewModel.PropertyChanged += OnMainViewModelPropertyChanged;
            }
        }

        private void OnMainViewModelPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MainViewModel.SelectedSite) && _mainViewModel?.SelectedSite != null)
            {
                UpdateChildViewModels(_mainViewModel.SelectedSite.Id);
            }
        }

        private void UpdateChildViewModels(string siteId)
        {
            // Update SiteView ViewModel
            if (FindName("SiteView") is SiteView siteView && 
                siteView.DataContext is SiteViewModel siteViewModel)
            {
                // SiteViewModel doesn't need SelectedSiteId as it manages all sites
            }

            // Update ApartmentView ViewModel
            if (FindName("ApartmentView") is ApartmentView apartmentView && 
                apartmentView.DataContext is ApartmentViewModel apartmentViewModel)
            {
                apartmentViewModel.SelectedSiteId = siteId;
            }

            // Update FlatView ViewModel
            if (FindName("FlatView") is FlatView flatView && 
                flatView.DataContext is FlatViewModel flatViewModel)
            {
                flatViewModel.SelectedSiteId = siteId;
            }

            // Update TenantView ViewModel
            if (FindName("TenantView") is TenantView tenantView && 
                tenantView.DataContext is TenantViewModel tenantViewModel)
            {
                tenantViewModel.SelectedSiteId = siteId;
            }

            // Update PaymentView ViewModel
            if (FindName("PaymentView") is PaymentView paymentView && 
                paymentView.DataContext is PaymentViewModel paymentViewModel)
            {
                paymentViewModel.SelectedSiteId = siteId;
            }
        }
    }
}
