<UserControl x:Class="ApartmanYonetimSistemi.Views.PaymentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources">
    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:PaymentViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Ödeme Listesi -->
        <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Ödemeler"
                         Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Filtre -->
                <ComboBox Grid.Row="1"
                        materialDesign:HintAssist.Hint="Filtre"
                        SelectedValue="{Binding PaymentFilter}"
                        SelectedValuePath="Tag"
                        Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                        Margin="0,0,0,16"
                        Width="200"
                        HorizontalAlignment="Left">
                    <ComboBoxItem Content="Tümü" Tag="All"/>
                    <ComboBoxItem Content="Ödenenler" Tag="Paid"/>
                    <ComboBoxItem Content="Ödenecekler" Tag="Due"/>
                    <ComboBoxItem Content="Gecikmiş" Tag="Overdue"/>
                </ComboBox>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="2" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Ödeme Listesi -->
                <DataGrid Grid.Row="3"
                        ItemsSource="{Binding Payments}"
                        SelectedItem="{Binding SelectedPayment, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Açıklama" Binding="{Binding Description}" Width="*"/>
                        <DataGridTextColumn Header="Tutar" Binding="{Binding Amount, StringFormat=C}" Width="Auto"/>
                        <DataGridTextColumn Header="Tip" Binding="{Binding Type}" Width="Auto"/>
                        <DataGridTextColumn Header="Vade Tarihi" Binding="{Binding DueDate, StringFormat=dd.MM.yyyy}" Width="Auto"/>
                        <DataGridTextColumn Header="Durum" Binding="{Binding Status}" Width="Auto"/>
                        <DataGridTextColumn Header="Ödeme Tarihi" Binding="{Binding PaidDate, StringFormat=dd.MM.yyyy}" Width="Auto"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="YENİ ÖDEME"
                          Command="{Binding NewPaymentCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="DÜZENLE"
                          Command="{Binding EditPaymentCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="ÖDENDİ"
                          Command="{Binding MarkAsPaidCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Background="{DynamicResource PrimaryHueLightBrush}"
                          Margin="0,0,8,0"/>
                    <Button Content="HATIRLATMA"
                          Command="{Binding SendReminderCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="AYLIK OLUŞTUR"
                          Command="{Binding GenerateMonthlyPaymentsCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="SİL"
                          Command="{Binding DeletePaymentCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Ödeme Detay/Düzenleme Formu -->
        <materialDesign:Card Grid.Column="1">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0"
                         Text="{Binding IsEditing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Ödeme Düzenle|Yeni Ödeme'}"
                         Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Daire Seçimi -->
                        <ComboBox materialDesign:HintAssist.Hint="Daire"
                                ItemsSource="{Binding Flats}"
                                SelectedValue="{Binding CurrentPayment.FlatId}"
                                SelectedValuePath="Id"
                                DisplayMemberPath="FlatNo"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16"/>

                        <!-- Tutar -->
                        <TextBox materialDesign:HintAssist.Hint="Tutar (₺)"
                               Text="{Binding CurrentPayment.Amount, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Ödeme Tipi -->
                        <ComboBox materialDesign:HintAssist.Hint="Ödeme Tipi"
                                SelectedValue="{Binding CurrentPayment.Type}"
                                SelectedValuePath="Tag"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16">
                            <ComboBoxItem Content="Kira" Tag="Rent"/>
                            <ComboBoxItem Content="Aidat" Tag="Dues"/>
                            <ComboBoxItem Content="Bakım" Tag="Maintenance"/>
                            <ComboBoxItem Content="Diğer" Tag="Other"/>
                        </ComboBox>

                        <!-- Vade Tarihi -->
                        <DatePicker materialDesign:HintAssist.Hint="Vade Tarihi"
                                  SelectedDate="{Binding CurrentPayment.DueDate}"
                                  Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="0,0,0,16"/>

                        <!-- Ödeme Tarihi -->
                        <DatePicker materialDesign:HintAssist.Hint="Ödeme Tarihi (Opsiyonel)"
                                  SelectedDate="{Binding CurrentPayment.PaidDate}"
                                  Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="0,0,0,16"/>

                        <!-- Durum -->
                        <ComboBox materialDesign:HintAssist.Hint="Durum"
                                SelectedValue="{Binding CurrentPayment.Status}"
                                SelectedValuePath="Tag"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16">
                            <ComboBoxItem Content="Ödendi" Tag="Paid"/>
                            <ComboBoxItem Content="Ödenecek" Tag="Due"/>
                            <ComboBoxItem Content="Gecikmiş" Tag="Overdue"/>
                            <ComboBoxItem Content="İptal" Tag="Cancelled"/>
                        </ComboBox>

                        <!-- Ödeme Yöntemi -->
                        <ComboBox materialDesign:HintAssist.Hint="Ödeme Yöntemi"
                                SelectedValue="{Binding CurrentPayment.PaymentMethod}"
                                SelectedValuePath="Tag"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16">
                            <ComboBoxItem Content="Nakit" Tag="Cash"/>
                            <ComboBoxItem Content="Banka Havalesi" Tag="BankTransfer"/>
                            <ComboBoxItem Content="Kredi Kartı" Tag="CreditCard"/>
                            <ComboBoxItem Content="Çek" Tag="Check"/>
                        </ComboBox>

                        <!-- Fiş Numarası -->
                        <TextBox materialDesign:HintAssist.Hint="Fiş Numarası"
                               Text="{Binding CurrentPayment.ReceiptNumber, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Açıklama -->
                        <TextBox materialDesign:HintAssist.Hint="Açıklama"
                               Text="{Binding CurrentPayment.Description, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               AcceptsReturn="True"
                               TextWrapping="Wrap"
                               MaxLength="500"
                               Height="80"
                               VerticalScrollBarVisibility="Auto"
                               Margin="0,0,0,16"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Margin="0,16,0,0">
                    <Button Content="KAYDET"
                          Command="{Binding SavePaymentCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,0,8"/>
                    <Button Content="İPTAL"
                          Command="{Binding CancelEditCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.ColumnSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>