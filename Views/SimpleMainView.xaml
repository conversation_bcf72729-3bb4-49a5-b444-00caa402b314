<UserControl x:Class="ApartmanYonetimSistemi.Views.SimpleMainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:views="clr-namespace:ApartmanYonetimSistemi.Views">
    <UserControl.DataContext>
        <vm:MainViewModel/>
    </UserControl.DataContext>

    <Grid>
        <!-- Modern Background -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#1e3c72" Offset="0"/>
                <GradientStop Color="#2a5298" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- Main Container -->
        <Border Background="White" CornerRadius="20" Margin="30" Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="10" Opacity="0.3" BlurRadius="20"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="80"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Modern Header -->
                <Border Grid.Row="0" Background="#2c3e50" CornerRadius="20,20,0,0">
                    <Grid Margin="30,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <Border Background="#3498db" CornerRadius="25" Width="50" Height="50" Margin="0,0,15,0">
                                <TextBlock Text="🏢" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="Apartman Yönetim Sistemi 2025"
                                           FontSize="18" FontWeight="Bold" Foreground="White"/>
                                <TextBlock Text="{Binding CurrentUser.Name, StringFormat='Hoş geldiniz, {0}'}"
                                           FontSize="12" Foreground="#bdc3c7"/>
                                <TextBlock Text="{Binding CurrentView, StringFormat='Aktif Sayfa: {0}'}"
                                           FontSize="10" Foreground="#95a5a6"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                            <Border Background="#27ae60" CornerRadius="15" Padding="10,5" Margin="0,0,10,0">
                                <TextBlock Text="🟢 Online" Foreground="White" FontSize="12" FontWeight="Bold"/>
                            </Border>
                            <Button Content="🚪 Çıkış" Command="{Binding LogoutCommand}"
                                    Background="#e74c3c" Foreground="White" BorderThickness="0"
                                    Padding="15,8" FontWeight="Bold" Cursor="Hand"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Modern Content Area -->
                <Grid Grid.Row="1" Margin="30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="280"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Modern Navigation Sidebar -->
                    <Border Grid.Column="0" Background="#f8f9fa" CornerRadius="15" Margin="0,0,20,0" Padding="20">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock Text="📋 Navigasyon" FontSize="16" FontWeight="Bold"
                                       Foreground="#2c3e50" Margin="0,0,0,20"/>

                            <!-- Dashboard Button -->
                            <Button Command="{Binding ShowDashboardCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#3498db"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#2980b9"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📊" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Dashboard" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <!-- Sites Button -->
                            <Button Command="{Binding ShowSitesCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#2ecc71"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#27ae60"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🏢" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Siteler" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <!-- Diğer butonları da ekleyelim -->
                            <Button Command="{Binding ShowApartmentsCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#f39c12"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#e67e22"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🏠" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Apartmanlar" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button Command="{Binding ShowFlatsCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#9b59b6"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#8e44ad"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🚪" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Daireler" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button Command="{Binding ShowTenantsCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#e67e22"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#d35400"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="👥" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Kiracılar" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button Command="{Binding ShowPaymentsCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#1abc9c"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#16a085"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💰" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Ödemeler" FontSize="14"/>
                                </StackPanel>
                            </Button>

                            <Button Command="{Binding ShowReportsCommand}" Margin="0,0,0,10" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="#34495e"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="10">
                                                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,12"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#2c3e50"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📈" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Raporlar" FontSize="14"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- Modern Content Area -->
                    <Border Grid.Column="1" Background="White" CornerRadius="15" Padding="30">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
                        </Border.Effect>

                        <Grid>
                            <!-- Modern Dashboard -->
                            <StackPanel Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Dashboard}">
                                <TextBlock Text="📊 Dashboard" FontSize="28" FontWeight="Bold"
                                           Foreground="#2c3e50" Margin="0,0,0,30"/>

                                <!-- Modern Stats Cards -->
                                <Grid Margin="0,0,0,30">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="#3498db" CornerRadius="15"
                                            Padding="25" Margin="0,0,15,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#3498db" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🏢" FontSize="40" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                            <TextBlock Text="Toplam Site" Foreground="White"
                                                       HorizontalAlignment="Center" FontWeight="Bold" FontSize="14"/>
                                            <TextBlock Text="{Binding UserSites.Count}" Foreground="White" FontSize="32"
                                                       FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="1" Background="#2ecc71" CornerRadius="15"
                                            Padding="25" Margin="7.5,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#2ecc71" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🏠" FontSize="40" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                            <TextBlock Text="Apartmanlar" Foreground="White"
                                                       HorizontalAlignment="Center" FontWeight="Bold" FontSize="14"/>
                                            <TextBlock Text="{Binding SiteApartments.Count}" Foreground="White" FontSize="32"
                                                       FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="2" Background="#e74c3c" CornerRadius="15"
                                            Padding="25" Margin="15,0,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#e74c3c" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                                        </Border.Effect>
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="💰" FontSize="40" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                            <TextBlock Text="Bu Ay Gelir" Foreground="White"
                                                       HorizontalAlignment="Center" FontWeight="Bold" FontSize="14"/>
                                            <TextBlock Text="₺25,000" Foreground="White" FontSize="32"
                                                       FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- Welcome Message -->
                                <Border Background="#f8f9fa" CornerRadius="10" Padding="20">
                                    <StackPanel>
                                        <TextBlock Text="🎉 Hoş Geldiniz!" FontSize="18" FontWeight="Bold"
                                                   Foreground="#2c3e50" Margin="0,0,0,10"/>
                                        <TextBlock Text="Apartman yönetim sisteminize hoş geldiniz. Sol menüden işlem yapmak istediğiniz bölümü seçebilirsiniz."
                                                   FontSize="14" Foreground="#7f8c8d" TextWrapping="Wrap"/>
                                        <TextBlock Text="Sistem tamamen fonksiyonel ve 2025 standartlarında tasarlanmıştır."
                                                   FontSize="12" Foreground="#95a5a6" Margin="0,10,0,0" FontStyle="Italic"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>

                            <!-- Other Views -->
                            <views:SiteView Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Sites}"/>
                            <views:ApartmentView Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Apartments}"/>
                            <views:FlatView Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Flats}"/>
                            <views:TenantView Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Tenants}"/>
                            <views:PaymentView Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Payments}"/>

                            <StackPanel Visibility="{Binding CurrentView, Converter={StaticResource StringToVisibilityConverter}, ConverterParameter=Reports}">
                                <TextBlock Text="📈 Raporlar" FontSize="28" FontWeight="Bold" Foreground="#2c3e50" Margin="0,0,0,20"/>
                                <Border Background="#f8f9fa" CornerRadius="10" Padding="20">
                                    <TextBlock Text="Rapor modülü yakında eklenecek. Bu bölümde gelir-gider raporları, kiracı raporları ve ödeme analizleri yer alacak."
                                               FontSize="14" Foreground="#7f8c8d" TextWrapping="Wrap"/>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
