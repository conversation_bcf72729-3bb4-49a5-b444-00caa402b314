<UserControl x:Class="ApartmanYonetimSistemi.Views.SiteView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels">
    <UserControl.DataContext>
        <vm:SiteViewModel/>
    </UserControl.DataContext>
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#3498db" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="Site Yönetimi" FontSize="24" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="Siteleri görüntüleyin, ekleyin ve düzenleyin" FontSize="14" Foreground="White" Opacity="0.8"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 Yenile" Command="{Binding LoadSitesCommand}"
                            Background="White" Foreground="#3498db" BorderThickness="0"
                            Padding="15,8" Margin="0,0,10,0" FontWeight="Bold" Cursor="Hand"/>
                    <Button Content="➕ Yeni Site" Command="{Binding NewSiteCommand}"
                            Background="#2ecc71" Foreground="White" BorderThickness="0"
                            Padding="15,8" FontWeight="Bold" Cursor="Hand"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sites List -->
            <Border Grid.Column="0" Background="White" CornerRadius="10" Margin="0,0,10,0" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                </Border.Effect>
                <StackPanel>
                    <TextBlock Text="Siteler" FontSize="18" FontWeight="Bold" Foreground="#333" Margin="0,0,0,15"/>

                    <DataGrid ItemsSource="{Binding Sites}" SelectedItem="{Binding SelectedSite}"
                            AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                            GridLinesVisibility="None" HeadersVisibility="Column" Background="Transparent"
                            BorderThickness="0" RowBackground="Transparent" AlternatingRowBackground="#f8f9fa">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Site Adı" Binding="{Binding SiteName}" Width="*"/>
                            <DataGridTextColumn Header="Adres" Binding="{Binding Address}" Width="2*"/>
                            <DataGridTextColumn Header="Durum" Binding="{Binding Status}" Width="Auto"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </Border>

            <!-- Site Details -->
            <Border Grid.Column="1" Background="White" CornerRadius="10" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.2" BlurRadius="8"/>
                </Border.Effect>
                <StackPanel>
                    <TextBlock Text="Site Detayları" FontSize="18" FontWeight="Bold" Foreground="#333" Margin="0,0,0,20"/>

                    <!-- Site Form -->
                    <StackPanel Visibility="{Binding IsEditing, Converter={StaticResource BoolToVisibilityConverter}}">
                        <TextBlock Text="Site Adı:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding CurrentSite.SiteName}" Padding="10" BorderBrush="#ddd" Margin="0,0,0,15"/>

                        <TextBlock Text="Adres:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding CurrentSite.Address}" AcceptsReturn="True" TextWrapping="Wrap"
                                MinLines="3" Padding="10" BorderBrush="#ddd" Margin="0,0,0,15"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="İptal" Command="{Binding CancelEditCommand}"
                                    Background="#95a5a6" Foreground="White" BorderThickness="0"
                                    Padding="15,8" Margin="0,0,10,0" FontWeight="Bold" Cursor="Hand"/>
                            <Button Content="Kaydet" Command="{Binding SaveSiteCommand}"
                                    Background="#27ae60" Foreground="White" BorderThickness="0"
                                    Padding="15,8" FontWeight="Bold" Cursor="Hand"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Site Info -->
                    <StackPanel Visibility="{Binding IsEditing, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                        <TextBlock Text="{Binding SelectedSite.SiteName}" FontSize="20" FontWeight="Bold"
                                   Foreground="#333" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding SelectedSite.Address}" FontSize="14" Foreground="#666"
                                   TextWrapping="Wrap" Margin="0,0,0,20"/>

                        <StackPanel Orientation="Horizontal">
                            <Button Content="✏️ Düzenle" Command="{Binding EditSiteCommand}"
                                    Background="#f39c12" Foreground="White" BorderThickness="0"
                                    Padding="15,8" Margin="0,0,10,0" FontWeight="Bold" Cursor="Hand"/>
                            <Button Content="🗑️ Sil" Command="{Binding DeleteSiteCommand}"
                                    Background="#e74c3c" Foreground="White" BorderThickness="0"
                                    Padding="15,8" FontWeight="Bold" Cursor="Hand"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Messages -->
                    <TextBlock Text="{Binding ErrorMessage}" Foreground="#e74c3c" FontWeight="Bold"
                               Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                               Margin="0,20,0,0"/>
                    <TextBlock Text="{Binding SuccessMessage}" Foreground="#27ae60" FontWeight="Bold"
                               Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                               Margin="0,20,0,0"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="2" Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="⏳" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="Yükleniyor..." Foreground="White" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl> 