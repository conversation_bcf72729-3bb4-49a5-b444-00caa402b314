<UserControl x:Class="ApartmanYonetimSistemi.Views.TenantView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ApartmanYonetimSistemi.Resources">
    <UserControl.Resources>
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <converters:BoolToStringConverter x:Key="BoolToStringConverter"/>
        <converters:NotNullToBoolConverter x:Key="NotNullToBoolConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:TenantViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Kiracı Listesi -->
        <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Başlık -->
                <TextBlock Grid.Row="0" Text="Kiracılar"
                         Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Hata/Başarı Mesajları -->
                <StackPanel Grid.Row="1" Margin="0,0,0,16">
                    <materialDesign:Card Background="{DynamicResource ValidationErrorBrush}"
                                       Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding ErrorMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>

                    <materialDesign:Card Background="{DynamicResource PrimaryHueMidBrush}"
                                       Visibility="{Binding SuccessMessage, Converter={StaticResource StringToVisibilityConverter}}"
                                       Margin="0,0,0,8">
                        <TextBlock Text="{Binding SuccessMessage}"
                                 Foreground="White"
                                 Margin="16,8"
                                 TextWrapping="Wrap"/>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Kiracı Listesi -->
                <DataGrid Grid.Row="2"
                        ItemsSource="{Binding Tenants}"
                        SelectedItem="{Binding SelectedTenant, Mode=TwoWay}"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Ad Soyad" Binding="{Binding FullName}" Width="*"/>
                        <DataGridTextColumn Header="Telefon" Binding="{Binding Phone}" Width="Auto"/>
                        <DataGridTextColumn Header="E-posta" Binding="{Binding Email}" Width="*"/>
                        <DataGridTextColumn Header="Giriş Tarihi" Binding="{Binding EntryDate, StringFormat=dd.MM.yyyy}" Width="Auto"/>
                        <DataGridCheckBoxColumn Header="Aktif" Binding="{Binding IsActive}" Width="Auto"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Butonlar -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                    <Button Content="YENİ KİRACI"
                          Command="{Binding NewTenantCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="DÜZENLE"
                          Command="{Binding EditTenantCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="HOŞ GELDİN E-POSTASI"
                          Command="{Binding SendWelcomeEmailCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"/>
                    <Button Content="SİL"
                          Command="{Binding DeleteTenantCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          Foreground="{DynamicResource ValidationErrorBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Kiracı Detay/Düzenleme Formu -->
        <materialDesign:Card Grid.Column="1">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Form Başlığı -->
                <TextBlock Grid.Row="0"
                         Text="{Binding IsEditing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Kiracı Düzenle|Yeni Kiracı'}"
                         Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                         Margin="0,0,0,16"/>

                <!-- Form Alanları -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- Ad -->
                        <TextBox materialDesign:HintAssist.Hint="Ad"
                               Text="{Binding CurrentTenant.Name, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Soyad -->
                        <TextBox materialDesign:HintAssist.Hint="Soyad"
                               Text="{Binding CurrentTenant.Surname, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Telefon -->
                        <TextBox materialDesign:HintAssist.Hint="Telefon"
                               Text="{Binding CurrentTenant.Phone, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- E-posta -->
                        <TextBox materialDesign:HintAssist.Hint="E-posta"
                               Text="{Binding CurrentTenant.Email, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- TC Kimlik No -->
                        <TextBox materialDesign:HintAssist.Hint="TC Kimlik Numarası"
                               Text="{Binding CurrentTenant.IdentityNumber, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               MaxLength="11"
                               Margin="0,0,0,16"/>

                        <!-- Daire Seçimi -->
                        <ComboBox materialDesign:HintAssist.Hint="Daire"
                                ItemsSource="{Binding AvailableFlats}"
                                SelectedValue="{Binding CurrentTenant.FlatId}"
                                SelectedValuePath="Id"
                                DisplayMemberPath="FlatNo"
                                Style="{DynamicResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,0,16"/>

                        <!-- Giriş Tarihi -->
                        <DatePicker materialDesign:HintAssist.Hint="Giriş Tarihi"
                                  SelectedDate="{Binding CurrentTenant.EntryDate}"
                                  Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="0,0,0,16"/>

                        <!-- Çıkış Tarihi -->
                        <DatePicker materialDesign:HintAssist.Hint="Çıkış Tarihi (Opsiyonel)"
                                  SelectedDate="{Binding CurrentTenant.ExitDate}"
                                  Style="{DynamicResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="0,0,0,16"/>

                        <!-- Acil Durum İletişim -->
                        <TextBox materialDesign:HintAssist.Hint="Acil Durum İletişim Kişisi"
                               Text="{Binding CurrentTenant.EmergencyContact, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Acil Durum Telefon -->
                        <TextBox materialDesign:HintAssist.Hint="Acil Durum Telefonu"
                               Text="{Binding CurrentTenant.EmergencyPhone, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,16"/>

                        <!-- Notlar -->
                        <TextBox materialDesign:HintAssist.Hint="Notlar"
                               Text="{Binding CurrentTenant.Notes, UpdateSourceTrigger=PropertyChanged}"
                               Style="{DynamicResource MaterialDesignFloatingHintTextBox}"
                               AcceptsReturn="True"
                               TextWrapping="Wrap"
                               MaxLength="1000"
                               Height="80"
                               VerticalScrollBarVisibility="Auto"
                               Margin="0,0,0,16"/>

                        <!-- Aktif Durumu -->
                        <CheckBox Content="Aktif"
                                IsChecked="{Binding CurrentTenant.IsActive}"
                                Style="{DynamicResource MaterialDesignCheckBox}"
                                Margin="0,8,0,0"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Form Butonları -->
                <StackPanel Grid.Row="2" Margin="0,16,0,0">
                    <Button Content="KAYDET"
                          Command="{Binding SaveTenantCommand}"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          Margin="0,0,0,8"/>
                    <Button Content="İPTAL"
                          Command="{Binding CancelEditCommand}"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Loading Overlay -->
        <Grid Grid.ColumnSpan="2"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="100" Height="100"
                           Style="{DynamicResource MaterialDesignCircularProgressBar}"/>
                <TextBlock Text="Yükleniyor..."
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>